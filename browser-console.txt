:8080/api/recording/start_test_case:1  Failed to load resource: the server responded with a status of 500 (INTERNAL SERVER ERROR)
:8080/api/recording/stop_test_case:1  Failed to load resource: the server responded with a status of 500 (INTERNAL SERVER ERROR)
:8080/api/recording/start_test_case:1  Failed to load resource: the server responded with a status of 500 (INTERNAL SERVER ERROR)
:8080/api/recording/stop_test_case:1  Failed to load resource: the server responded with a status of 500 (INTERNAL SERVER ERROR)
:8080/api/recording/start_test_case:1  Failed to load resource: the server responded with a status of 500 (INTERNAL SERVER ERROR)
:8080/api/recording/stop_test_case:1  Failed to load resource: the server responded with a status of 500 (INTERNAL SERVER ERROR)
:8080/api/recording/start_test_case:1  Failed to load resource: the server responded with a status of 500 (INTERNAL SERVER ERROR)
:8080/api/recording/stop_test_case:1  Failed to load resource: the server responded with a status of 500 (INTERNAL SERVER ERROR)
:8080/api/recording/start_test_case:1  Failed to load resource: the server responded with a status of 500 (INTERNAL SERVER ERROR)
:8080/api/recording/stop_test_case:1  Failed to load resource: the server responded with a status of 500 (INTERNAL SERVER ERROR)
:8080/api/recording/start_test_case:1  Failed to load resource: the server responded with a status of 500 (INTERNAL SERVER ERROR)
:8080/api/recording/stop_test_case:1  Failed to load resource: the server responded with a status of 500 (INTERNAL SERVER ERROR)
:8080/api/recording/start_test_case:1  Failed to load resource: the server responded with a status of 500 (INTERNAL SERVER ERROR)
:8080/api/recording/stop_test_case:1  Failed to load resource: the server responded with a status of 500 (INTERNAL SERVER ERROR)
:8080/api/recording/start_test_case:1  Failed to load resource: the server responded with a status of 500 (INTERNAL SERVER ERROR)
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432488290
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
execution-manager.js:878 Added test_idx=7 to multi-step action data for execution
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432492566
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
execution-manager.js:878 Added test_idx=7 to multi-step action data for execution
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432496687
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
execution-manager.js:878 Added test_idx=7 to multi-step action data for execution
execution-manager.js:243 Performing periodic Appium session health check
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432509656
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
execution-manager.js:878 Added test_idx=7 to multi-step action data for execution
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432512551
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432513054
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2768 [info] Executing action 448/641: Restart app: env[appid]
main.js?v=**********:2814 [Action Status] XEbZHdi0GT=running
execution-manager.js:3277 === EXECUTION MANAGER RECORDING STOP DEBUG ===
execution-manager.js:3278 Test case filename: Postcode_Flow_20250502104451.json
:8080/api/recording/stop_test_case:1  Failed to load resource: the server responded with a status of 500 (INTERNAL SERVER ERROR)
execution-manager.js:3292 Recording stop result: Object
execution-manager.js:3298 Failed to stop video recording for test case Postcode_Flow_20250502104451.json: No screen recording in progress
ExecutionManager._stopTestCaseRecording @ execution-manager.js:3298
execution-manager.js:3176 Updating TC State for testIdx 7: Old Status: running, New Status: passed, Error: null
execution-manager.js:636 Checking test case: App Settings AU
execution-manager.js:637 Test case header classes: Array(7)
execution-manager.js:638 Is already passed: false
execution-manager.js:3234 === EXECUTION MANAGER RECORDING START DEBUG ===
execution-manager.js:3235 Test case filename: App_Settings_AU_20250609145542.json
execution-manager.js:3236 Recording enabled: true
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
:8080/api/recording/start_test_case:1  Failed to load resource: the server responded with a status of 500 (INTERNAL SERVER ERROR)
execution-manager.js:3257 Recording start result: Object
execution-manager.js:3262 Failed to start video recording for test case App_Settings_AU_20250609145542.json: Screen recording failed: FFmpeg is required but not installed. Please install FFmpeg using "brew install ffmpeg" and try again.
ExecutionManager._startTestCaseRecording @ execution-manager.js:3262
execution-manager.js:683 Marked test case test-case-App-Settings-AU-20250609145542-json as current with test_idx=8
execution-manager.js:3176 Updating TC State for testIdx 8: Old Status: pending, New Status: running, Error: null
execution-manager.js:692 Will use test_idx=8 for actions in this test case
execution-manager.js:1122 Detected new test case at index 447. Previous test case: test-case-Postcode-Flow-20250502104451-json, Current test case: test-case-App-Settings-AU-20250609145542-json
execution-manager.js:1126 Adding a 10-second delay before starting a new test case at index 447
main.js?v=**********:5589 Test case "App Settings AU" expanded
execution-manager.js:1131 Delay completed, continuing with test case at index 447
execution-manager.js:1137 Added test_idx=8 to action data for execution
execution-manager.js:65 Execution context loaded: Object
execution-manager.js:1181 Executing action with parameters: Object
main.js?v=**********:2814 [Action Status] XEbZHdi0GT=pass
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=*************
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2768 [info] Executing action 449/641: Tap on element with xpath: //XCUIElementTypeButton[@name="txtHomeAccountCtaSignIn"]
main.js?v=**********:2814 [Action Status] veukWo4573=running
execution-manager.js:1137 Added test_idx=8 to action data for execution
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
execution-manager.js:65 Execution context loaded: Object
execution-manager.js:1181 Executing action with parameters: Object
main.js?v=**********:2814 [Action Status] veukWo4573=pass
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=*************
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing action 450/641: iOS Function: alert_accept
main.js?v=**********:2814 [Action Status] rJ86z4njuR=running
execution-manager.js:1137 Added test_idx=8 to action data for execution
execution-manager.js:65 Execution context loaded: Object
execution-manager.js:1181 Executing action with parameters: Object
main.js?v=**********:2814 [Action Status] rJ86z4njuR=pass
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432539926
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing action 451/641: Execute Test Case: Kmart-Signin (5 steps)
main.js?v=**********:2814 [Action Status] gx05zu87DK=running
main.js?v=**********:2768 [info] Loading steps for multiStep action: Kmart-Signin
main.js?v=**********:2768 [info] Loaded 5 steps from test case: Kmart-Signin
main.js?v=**********:2768 [info] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
execution-manager.js:872 Adding a 2-second delay before executing multi-step action for test_idx=8
execution-manager.js:874 Delay completed, continuing with multi-step action for test_idx=8
execution-manager.js:878 Added test_idx=8 to multi-step action data for execution
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432545992
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
execution-manager.js:878 Added test_idx=8 to multi-step action data for execution
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432550528
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing Multi Step action step 3/5: iOS Function: text - Text: "<EMAIL>"
execution-manager.js:878 Added test_idx=8 to multi-step action data for execution
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432555525
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
execution-manager.js:878 Added test_idx=8 to multi-step action data for execution
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432560012
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
execution-manager.js:878 Added test_idx=8 to multi-step action data for execution
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432564892
main.js?v=**********:2768 [success] Screenshot refreshed
execution-manager.js:243 Performing periodic Appium session health check
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432565394
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2768 [info] Executing action 452/641: Terminate app: com.apple.Preferences
main.js?v=**********:2814 [Action Status] mIKA85kXaW=running
execution-manager.js:1137 Added test_idx=8 to action data for execution
execution-manager.js:65 Execution context loaded: Object
execution-manager.js:1181 Executing action with parameters: Object
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2814 [Action Status] mIKA85kXaW=pass
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432567028
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing action 453/641: Launch app: com.apple.Preferences
main.js?v=**********:2814 [Action Status] LfyQctrEJn=running
execution-manager.js:1137 Added test_idx=8 to action data for execution
execution-manager.js:65 Execution context loaded: Object
execution-manager.js:1181 Executing action with parameters: Object
main.js?v=**********:2814 [Action Status] LfyQctrEJn=pass
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432570080
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing action 454/641: Tap on Text: "Wi-Fi"
main.js?v=**********:2814 [Action Status] w1RV76df9x=running
execution-manager.js:1137 Added test_idx=8 to action data for execution
execution-manager.js:65 Execution context loaded: Object
execution-manager.js:1181 Executing action with parameters: Object
main.js?v=**********:2814 [Action Status] w1RV76df9x=pass
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432574586
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing action 455/641: Wait for 5 ms
main.js?v=**********:2814 [Action Status] V42eHtTRYW=running
execution-manager.js:1137 Added test_idx=8 to action data for execution
execution-manager.js:65 Execution context loaded: Object
execution-manager.js:1181 Executing action with parameters: Object
main.js?v=**********:2814 [Action Status] V42eHtTRYW=pass
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432581404
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing action 456/641: Tap on element with xpath: //XCUIElementTypeSwitch[@name="Wi‑Fi"]
main.js?v=**********:2814 [Action Status] jUCAk6GJc4=running
execution-manager.js:1137 Added test_idx=8 to action data for execution
execution-manager.js:65 Execution context loaded: Object
execution-manager.js:1181 Executing action with parameters: Object
main.js?v=**********:2814 [Action Status] jUCAk6GJc4=pass
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432584197
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing action 457/641: Wait for 5 ms
main.js?v=**********:2814 [Action Status] V42eHtTRYW=running
execution-manager.js:1137 Added test_idx=8 to action data for execution
execution-manager.js:65 Execution context loaded: Object
execution-manager.js:1181 Executing action with parameters: Object
main.js?v=**********:2814 [Action Status] V42eHtTRYW=pass
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432590996
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing action 458/641: Restart app: env[appid]
main.js?v=**********:2814 [Action Status] oSQ8sPdVOJ=running
execution-manager.js:1137 Added test_idx=8 to action data for execution
execution-manager.js:65 Execution context loaded: Object
execution-manager.js:1181 Executing action with parameters: Object
main.js?v=**********:2814 [Action Status] oSQ8sPdVOJ=pass
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432596037
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing action 459/641: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
main.js?v=**********:2814 [Action Status] cokvFXhj4c=running
execution-manager.js:1137 Added test_idx=8 to action data for execution
execution-manager.js:65 Execution context loaded: Object
execution-manager.js:1181 Executing action with parameters: Object
main.js?v=**********:2814 [Action Status] cokvFXhj4c=pass
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432598116
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing action 460/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"2 of 5")]
main.js?v=**********:2814 [Action Status] 3KNqlNy6Bj=running
execution-manager.js:1137 Added test_idx=8 to action data for execution
execution-manager.js:65 Execution context loaded: Object
execution-manager.js:1181 Executing action with parameters: Object
main.js?v=**********:2814 [Action Status] 3KNqlNy6Bj=pass
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432600812
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing action 461/641: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
main.js?v=**********:2814 [Action Status] eSr9EFlJek=running
execution-manager.js:1137 Added test_idx=8 to action data for execution
execution-manager.js:65 Execution context loaded: Object
execution-manager.js:1181 Executing action with parameters: Object
main.js?v=**********:2814 [Action Status] eSr9EFlJek=pass
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432602872
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing action 462/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"3 of 5")]
main.js?v=**********:2814 [Action Status] 6xgrAWyfZ4=running
execution-manager.js:1137 Added test_idx=8 to action data for execution
execution-manager.js:65 Execution context loaded: Object
execution-manager.js:1181 Executing action with parameters: Object
main.js?v=**********:2814 [Action Status] 6xgrAWyfZ4=pass
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432605346
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing action 463/641: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
main.js?v=**********:2814 [Action Status] WoymrHdtrO=running
execution-manager.js:1137 Added test_idx=8 to action data for execution
execution-manager.js:65 Execution context loaded: Object
execution-manager.js:1181 Executing action with parameters: Object
main.js?v=**********:2814 [Action Status] WoymrHdtrO=pass
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432607365
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing action 464/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"4 of 5")]
main.js?v=**********:2814 [Action Status] UpUSVInizv=running
execution-manager.js:1137 Added test_idx=8 to action data for execution
execution-manager.js:65 Execution context loaded: Object
execution-manager.js:1181 Executing action with parameters: Object
main.js?v=**********:2814 [Action Status] UpUSVInizv=pass
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432609860
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing action 465/641: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
main.js?v=**********:2814 [Action Status] seQcUKjkSU=running
execution-manager.js:1137 Added test_idx=8 to action data for execution
execution-manager.js:65 Execution context loaded: Object
execution-manager.js:1181 Executing action with parameters: Object
main.js?v=**********:2814 [Action Status] seQcUKjkSU=pass
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432611855
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing action 466/641: Launch app: com.apple.Preferences
main.js?v=**********:2814 [Action Status] LfyQctrEJn=running
execution-manager.js:1137 Added test_idx=8 to action data for execution
execution-manager.js:65 Execution context loaded: Object
execution-manager.js:1181 Executing action with parameters: Object
main.js?v=**********:2814 [Action Status] LfyQctrEJn=pass
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432613772
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing action 467/641: Wait for 5 ms
main.js?v=**********:2814 [Action Status] V42eHtTRYW=running
execution-manager.js:1137 Added test_idx=8 to action data for execution
execution-manager.js:65 Execution context loaded: Object
execution-manager.js:1181 Executing action with parameters: Object
main.js?v=**********:2814 [Action Status] V42eHtTRYW=pass
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432620577
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing action 468/641: Tap on element with xpath: //XCUIElementTypeSwitch[@name="Wi‑Fi"]
main.js?v=**********:2814 [Action Status] GRwHMVK4sA=running
execution-manager.js:1137 Added test_idx=8 to action data for execution
execution-manager.js:65 Execution context loaded: Object
execution-manager.js:1181 Executing action with parameters: Object
main.js?v=**********:2814 [Action Status] GRwHMVK4sA=pass
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432623177
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing action 469/641: Wait for 5 ms
main.js?v=**********:2814 [Action Status] V42eHtTRYW=running
execution-manager.js:1137 Added test_idx=8 to action data for execution
execution-manager.js:65 Execution context loaded: Object
execution-manager.js:1181 Executing action with parameters: Object
execution-manager.js:243 Performing periodic Appium session health check
main.js?v=**********:2814 [Action Status] V42eHtTRYW=pass
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432629981
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing action 470/641: Restart app: env[appid]
main.js?v=**********:2814 [Action Status] hCCEvRtj1A=running
execution-manager.js:1137 Added test_idx=8 to action data for execution
execution-manager.js:65 Execution context loaded: Object
execution-manager.js:1181 Executing action with parameters: Object
main.js?v=**********:2814 [Action Status] hCCEvRtj1A=pass
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432635151
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing action 471/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"5 of 5")]
main.js?v=**********:2814 [Action Status] UpUSVInizv=running
execution-manager.js:1137 Added test_idx=8 to action data for execution
execution-manager.js:65 Execution context loaded: Object
execution-manager.js:1181 Executing action with parameters: Object
main.js?v=**********:2814 [Action Status] UpUSVInizv=pass
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432640450
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing action 472/641: Swipe from (50%, 70%) to (50%, 30%)
main.js?v=**********:2814 [Action Status] ZZPNqTJ65s=running
execution-manager.js:1137 Added test_idx=8 to action data for execution
execution-manager.js:65 Execution context loaded: Object
execution-manager.js:1181 Executing action with parameters: Object
main.js?v=**********:2814 [Action Status] ZZPNqTJ65s=pass
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432645070
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing action 473/641: Tap on Text: "out"
main.js?v=**********:2814 [Action Status] LcYLwUffqj=running
execution-manager.js:1137 Added test_idx=8 to action data for execution
execution-manager.js:65 Execution context loaded: Object
execution-manager.js:1181 Executing action with parameters: Object
main.js?v=**********:2814 [Action Status] LcYLwUffqj=pass
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432649635
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing action 474/641: Restart app: com.apple.mobilesafari
main.js?v=**********:2814 [Action Status] xVuuejtCFA=running
execution-manager.js:1137 Added test_idx=8 to action data for execution
execution-manager.js:65 Execution context loaded: Object
execution-manager.js:1181 Executing action with parameters: Object
main.js?v=**********:2814 [Action Status] xVuuejtCFA=pass
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432653638
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing action 475/641: Tap on element with xpath: //XCUIElementTypeTextField[@name="TabBarItemTitle"]
main.js?v=**********:2814 [Action Status] 0Q0fm6OTij=running
execution-manager.js:1137 Added test_idx=8 to action data for execution
execution-manager.js:65 Execution context loaded: Object
execution-manager.js:1181 Executing action with parameters: Object
main.js?v=**********:2814 [Action Status] 0Q0fm6OTij=pass
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432656521
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing action 476/641: iOS Function: text - Text: "kmart au"
main.js?v=**********:2814 [Action Status] rYJcLPh8Aq=running
execution-manager.js:1137 Added test_idx=8 to action data for execution
execution-manager.js:65 Execution context loaded: Object
execution-manager.js:1181 Executing action with parameters: Object
main.js?v=**********:2814 [Action Status] rYJcLPh8Aq=pass
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432660059
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing action 477/641: Tap on element with xpath: //XCUIElementTypeStaticText[@name="https://www.kmart.com.au"]
main.js?v=**********:2814 [Action Status] fTdGMJ3NH3=running
execution-manager.js:1137 Added test_idx=8 to action data for execution
execution-manager.js:65 Execution context loaded: Object
execution-manager.js:1181 Executing action with parameters: Object
main.js?v=**********:2814 [Action Status] fTdGMJ3NH3=pass
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=*************
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing action 478/641: Check if element with xpath="//XCUIElementTypeButton[@name="txtHomeAccountCtaSignIn"]" exists
main.js?v=**********:2814 [Action Status] 0QtNHB5WEK=running
execution-manager.js:1137 Added test_idx=8 to action data for execution
execution-manager.js:65 Execution context loaded: Object
execution-manager.js:1181 Executing action with parameters: Object
main.js?v=**********:2814 [Action Status] 0QtNHB5WEK=pass
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=*************
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing action 479/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"2 of 5")]
main.js?v=**********:2814 [Action Status] UpUSVInizv=running
execution-manager.js:1137 Added test_idx=8 to action data for execution
execution-manager.js:65 Execution context loaded: Object
execution-manager.js:1181 Executing action with parameters: Object
main.js?v=**********:2814 [Action Status] UpUSVInizv=pass
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432670996
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing action 480/641: Swipe from (50%, 50%) to (50%, 30%)
main.js?v=**********:2814 [Action Status] QUeGIASAxV=running
execution-manager.js:1137 Added test_idx=8 to action data for execution
execution-manager.js:65 Execution context loaded: Object
execution-manager.js:1181 Executing action with parameters: Object
main.js?v=**********:2814 [Action Status] QUeGIASAxV=pass
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432674952
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing action 481/641: Tap on Text: "Catalogue"
main.js?v=**********:2814 [Action Status] gkkQzTCmma=running
execution-manager.js:1137 Added test_idx=8 to action data for execution
execution-manager.js:65 Execution context loaded: Object
execution-manager.js:1181 Executing action with parameters: Object
main.js?v=**********:2814 [Action Status] gkkQzTCmma=pass
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432679612
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing action 482/641: Tap if locator exists: xpath="//XCUIElementTypeOther[@name="Kmart Catalogue"]/XCUIElementTypeImage[1]"
main.js?v=**********:2814 [Action Status] Jh6RTFWeOU=running
execution-manager.js:1137 Added test_idx=8 to action data for execution
execution-manager.js:65 Execution context loaded: Object
execution-manager.js:1181 Executing action with parameters: Object
execution-manager.js:243 Performing periodic Appium session health check
main.js?v=**********:2814 [Action Status] Jh6RTFWeOU=pass
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432702823
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing action 483/641: Tap on image: env[catalogue-menu-img]
main.js?v=**********:2814 [Action Status] igReeDqips=running
execution-manager.js:1137 Added test_idx=8 to action data for execution
execution-manager.js:65 Execution context loaded: Object
execution-manager.js:1181 Executing action with parameters: Object
main.js?v=**********:2814 [Action Status] igReeDqips=pass
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432707905
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing action 484/641: Tap on Text: "List"
main.js?v=**********:2814 [Action Status] Pd7cReoJM6=running
execution-manager.js:1137 Added test_idx=8 to action data for execution
execution-manager.js:65 Execution context loaded: Object
execution-manager.js:1181 Executing action with parameters: Object
main.js?v=**********:2814 [Action Status] Pd7cReoJM6=pass
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432713790
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing action 485/641: Tap on element with xpath: (//XCUIElementTypeOther[@name="Kmart Catalogue"]//XCUIElementTypeStaticText[contains(@name,"$")])[1]
main.js?v=**********:2814 [Action Status] JcAR0JctQ6=running
execution-manager.js:1137 Added test_idx=8 to action data for execution
execution-manager.js:65 Execution context loaded: Object
execution-manager.js:1181 Executing action with parameters: Object
main.js?v=**********:2814 [Action Status] JcAR0JctQ6=pass
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432718005
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing action 486/641: Swipe from (50%, 70%) to (50%, 30%)
main.js?v=**********:2814 [Action Status] ZZPNqTJ65s=running
execution-manager.js:1137 Added test_idx=8 to action data for execution
execution-manager.js:65 Execution context loaded: Object
execution-manager.js:1181 Executing action with parameters: Object
main.js?v=**********:2814 [Action Status] ZZPNqTJ65s=pass
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432723648
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing action 487/641: Tap on element with accessibility_id: Add to bag
main.js?v=**********:2814 [Action Status] Cmvm82hiAa=running
execution-manager.js:1137 Added test_idx=8 to action data for execution
execution-manager.js:65 Execution context loaded: Object
execution-manager.js:1181 Executing action with parameters: Object
main.js?v=**********:2814 [Action Status] Cmvm82hiAa=pass
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432731185
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing action 488/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"5 of 5")]
main.js?v=**********:2814 [Action Status] UpUSVInizv=running
execution-manager.js:1137 Added test_idx=8 to action data for execution
execution-manager.js:65 Execution context loaded: Object
execution-manager.js:1181 Executing action with parameters: Object
main.js?v=**********:2814 [Action Status] UpUSVInizv=pass
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432735958
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing action 489/641: Tap if locator exists: xpath="//XCUIElementTypeStaticText[@name="Chat now"]/preceding-sibling::XCUIElementTypeImage[1]"
main.js?v=**********:2814 [Action Status] VpOhIxEl53=running
execution-manager.js:1137 Added test_idx=8 to action data for execution
execution-manager.js:65 Execution context loaded: Object
execution-manager.js:1181 Executing action with parameters: Object
execution-manager.js:243 Performing periodic Appium session health check
main.js?v=**********:2814 [Action Status] VpOhIxEl53=pass
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432749065
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing action 490/641: Tap on Text: "Catalogue"
main.js?v=**********:2814 [Action Status] gkkQzTCmma=running
execution-manager.js:1137 Added test_idx=8 to action data for execution
execution-manager.js:65 Execution context loaded: Object
execution-manager.js:1181 Executing action with parameters: Object
main.js?v=**********:5593 Test case "App Settings AU" collapsed
main.js?v=**********:2814 [Action Status] gkkQzTCmma=pass
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432753991
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing action 491/641: Tap if locator exists: xpath="//XCUIElementTypeOther[@name="Kmart Catalogue"]/XCUIElementTypeImage[1]"
main.js?v=**********:2814 [Action Status] gcSsGpqKwk=running
execution-manager.js:1137 Added test_idx=8 to action data for execution
execution-manager.js:65 Execution context loaded: Object
execution-manager.js:1181 Executing action with parameters: Object
main.js?v=**********:5593 Test case "Postcode Flow" collapsed
main.js?v=**********:5593 Test case "Others" collapsed
main.js?v=**********:5593 Test case "AU- MyAccount" collapsed
main.js?v=**********:2814 [Action Status] gcSsGpqKwk=pass
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=*************
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing action 492/641: Tap on image: env[catalogue-menu-img]
main.js?v=**********:2814 [Action Status] igReeDqips=running
execution-manager.js:1137 Added test_idx=8 to action data for execution
execution-manager.js:65 Execution context loaded: Object
execution-manager.js:1181 Executing action with parameters: Object
main.js?v=**********:2814 [Action Status] igReeDqips=pass
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432782247
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing action 493/641: Tap on Text: "List"
main.js?v=**********:2814 [Action Status] YHaMIjULRf=running
execution-manager.js:1137 Added test_idx=8 to action data for execution
execution-manager.js:65 Execution context loaded: Object
execution-manager.js:1181 Executing action with parameters: Object
main.js?v=**********:2814 [Action Status] YHaMIjULRf=pass
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432788075
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing action 494/641: Tap on element with xpath: (//XCUIElementTypeOther[@name="Kmart Catalogue"]//XCUIElementTypeStaticText[contains(@name,"$")])[1]
main.js?v=**********:2814 [Action Status] Qy0Y0uJchm=running
execution-manager.js:1137 Added test_idx=8 to action data for execution
execution-manager.js:65 Execution context loaded: Object
execution-manager.js:1181 Executing action with parameters: Object
main.js?v=**********:2814 [Action Status] Qy0Y0uJchm=pass
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432792259
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing action 495/641: Tap on element with accessibility_id: Add to bag
main.js?v=**********:2814 [Action Status] Iab9zCfpqO=running
execution-manager.js:1137 Added test_idx=8 to action data for execution
execution-manager.js:65 Execution context loaded: Object
execution-manager.js:1181 Executing action with parameters: Object
execution-manager.js:243 Performing periodic Appium session health check
main.js?v=**********:2814 [Action Status] Iab9zCfpqO=pass
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432813004
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing action 496/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"4 of 5")]
main.js?v=**********:2814 [Action Status] UpUSVInizv=running
execution-manager.js:1137 Added test_idx=8 to action data for execution
execution-manager.js:65 Execution context loaded: Object
execution-manager.js:1181 Executing action with parameters: Object
main.js?v=**********:2814 [Action Status] UpUSVInizv=pass
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432818565
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing action 497/641: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
main.js?v=**********:2814 [Action Status] saiPPHQSPa=running
execution-manager.js:1137 Added test_idx=8 to action data for execution
execution-manager.js:65 Execution context loaded: Object
execution-manager.js:1181 Executing action with parameters: Object
main.js?v=**********:2814 [Action Status] saiPPHQSPa=pass
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432830829
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing action 498/641: Tap on element with xpath: //XCUIElementTypeButton[@name="Increase quantity"]
main.js?v=**********:2814 [Action Status] DbM0d0m6rU=running
execution-manager.js:1137 Added test_idx=8 to action data for execution
execution-manager.js:65 Execution context loaded: Object
execution-manager.js:1181 Executing action with parameters: Object
main.js?v=**********:2814 [Action Status] DbM0d0m6rU=pass
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432835374
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing action 499/641: Tap on element with xpath: //XCUIElementTypeButton[@name="Decrease quantity"]
main.js?v=**********:2814 [Action Status] IW6uAwdtiW=running
execution-manager.js:1137 Added test_idx=8 to action data for execution
execution-manager.js:65 Execution context loaded: Object
execution-manager.js:1181 Executing action with parameters: Object
main.js?v=**********:2814 [Action Status] IW6uAwdtiW=pass
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432840138
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing action 500/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
main.js?v=**********:2814 [Action Status] K0c1gL9UK1=running
execution-manager.js:1137 Added test_idx=8 to action data for execution
execution-manager.js:65 Execution context loaded: Object
execution-manager.js:1181 Executing action with parameters: Object
main.js?v=**********:2814 [Action Status] K0c1gL9UK1=pass
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432845758
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing action 501/641: Tap on image: banner-close-updated.png
main.js?v=**********:2814 [Action Status] 3NOS1fbxZs=running
execution-manager.js:1137 Added test_idx=8 to action data for execution
execution-manager.js:65 Execution context loaded: Object
execution-manager.js:1181 Executing action with parameters: Object
main.js?v=**********:2814 [Action Status] 3NOS1fbxZs=pass
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432849921
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing action 502/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"1 of 5")]
main.js?v=**********:2814 [Action Status] 3KNqlNy6Bj=running
execution-manager.js:1137 Added test_idx=8 to action data for execution
execution-manager.js:65 Execution context loaded: Object
execution-manager.js:1181 Executing action with parameters: Object
main.js?v=**********:2814 [Action Status] 3KNqlNy6Bj=pass
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432854905
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing action 503/641: Swipe up till element xpath: "(//XCUIElementTypeScrollView)[4]/following-sibling::XCUIElementTypeOther[1]" is visible
main.js?v=**********:2814 [Action Status] OKiI82VdnE=running
execution-manager.js:1137 Added test_idx=8 to action data for execution
execution-manager.js:65 Execution context loaded: Object
execution-manager.js:1181 Executing action with parameters: Object
main.js?v=**********:2814 [Action Status] OKiI82VdnE=pass
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432861839
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing action 504/641: Tap on element with xpath: //XCUIElementTypeButton[@name="Home & Living"]
main.js?v=**********:2814 [Action Status] L59V5hqMX9=running
execution-manager.js:1137 Added test_idx=8 to action data for execution
execution-manager.js:65 Execution context loaded: Object
execution-manager.js:1181 Executing action with parameters: Object
execution-manager.js:243 Performing periodic Appium session health check
main.js?v=**********:2814 [Action Status] L59V5hqMX9=pass
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432866431
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing action 505/641: Tap on element with xpath: (//XCUIElementTypeScrollView)[4]/following-sibling::XCUIElementTypeOther[1]
main.js?v=**********:2814 [Action Status] n57KEWjTea=running
execution-manager.js:1137 Added test_idx=8 to action data for execution
execution-manager.js:65 Execution context loaded: Object
execution-manager.js:1181 Executing action with parameters: Object
main.js?v=**********:2814 [Action Status] n57KEWjTea=pass
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432871101
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing action 506/641: Tap on element with xpath: (//XCUIElementTypeScrollView/following-sibling::XCUIElementTypeImage[contains(@name,"$")])[1]
main.js?v=**********:2814 [Action Status] 2hGhWulI52=running
execution-manager.js:1137 Added test_idx=8 to action data for execution
execution-manager.js:65 Execution context loaded: Object
execution-manager.js:1181 Executing action with parameters: Object
main.js?v=**********:2814 [Action Status] 2hGhWulI52=pass
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432875328
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing action 507/641: Swipe from (50%, 50%) to (50%, 30%)
main.js?v=**********:2814 [Action Status] MA2re5cDWr=running
execution-manager.js:1137 Added test_idx=8 to action data for execution
execution-manager.js:65 Execution context loaded: Object
execution-manager.js:1181 Executing action with parameters: Object
main.js?v=**********:2814 [Action Status] MA2re5cDWr=pass
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432884355
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing action 508/641: Tap if locator exists: accessibility_id="Add to bag"
main.js?v=**********:2814 [Action Status] Teyz3d55XS=running
execution-manager.js:1137 Added test_idx=8 to action data for execution
execution-manager.js:65 Execution context loaded: Object
execution-manager.js:1181 Executing action with parameters: Object
main.js?v=**********:2814 [Action Status] Teyz3d55XS=pass
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432893290
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing action 509/641: Restart app: env[appid]
main.js?v=**********:2814 [Action Status] c4T3INQkzn=running
execution-manager.js:1137 Added test_idx=8 to action data for execution
execution-manager.js:65 Execution context loaded: Object
execution-manager.js:1181 Executing action with parameters: Object
main.js?v=**********:2814 [Action Status] c4T3INQkzn=pass
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432898539
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing action 510/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"4 of 5")]
main.js?v=**********:2814 [Action Status] UpUSVInizv=running
execution-manager.js:1137 Added test_idx=8 to action data for execution
execution-manager.js:65 Execution context loaded: Object
execution-manager.js:1181 Executing action with parameters: Object
main.js?v=**********:2814 [Action Status] UpUSVInizv=pass
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432903185
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing action 511/641: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
main.js?v=**********:2814 [Action Status] P26OyuqWlb=running
execution-manager.js:1137 Added test_idx=8 to action data for execution
execution-manager.js:65 Execution context loaded: Object
execution-manager.js:1181 Executing action with parameters: Object
main.js?v=**********:2814 [Action Status] P26OyuqWlb=pass
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432916340
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing action 512/641: Swipe from (50%, 50%) to (50%, 30%)
main.js?v=**********:2814 [Action Status] K2w7X1cPdH=running
execution-manager.js:1137 Added test_idx=8 to action data for execution
execution-manager.js:65 Execution context loaded: Object
execution-manager.js:1181 Executing action with parameters: Object
main.js?v=**********:2814 [Action Status] K2w7X1cPdH=pass
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432921998
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing action 513/641: Tap if locator exists: xpath="//XCUIElementTypeButton[contains(@name,"Remove")]"
main.js?v=**********:2814 [Action Status] yxlzTytgFT=running
execution-manager.js:1137 Added test_idx=8 to action data for execution
execution-manager.js:65 Execution context loaded: Object
execution-manager.js:1181 Executing action with parameters: Object
execution-manager.js:243 Performing periodic Appium session health check
main.js?v=**********:2814 [Action Status] yxlzTytgFT=pass
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432927476
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing action 514/641: Wait for 5 ms
main.js?v=**********:2814 [Action Status] Qb1AArnpCH=running
execution-manager.js:1137 Added test_idx=8 to action data for execution
execution-manager.js:65 Execution context loaded: Object
execution-manager.js:1181 Executing action with parameters: Object
main.js?v=**********:2814 [Action Status] Qb1AArnpCH=pass
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432934291
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing action 515/641: cleanupSteps action
main.js?v=**********:2814 [Action Status] kPdSiomhwu=running
main.js?v=**********:2768 [info] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
main.js?v=**********:2768 [info] Loaded 6 steps from test case: Kmart_AU_Cleanup
main.js?v=**********:2768 [info] Executing Multi Step action step 1/6: Restart app: au.com.kmart
execution-manager.js:872 Adding a 2-second delay before executing multi-step action for test_idx=8
execution-manager.js:874 Delay completed, continuing with multi-step action for test_idx=8
execution-manager.js:878 Added test_idx=8 to multi-step action data for execution
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432941556
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing Multi Step action step 2/6: Wait for 5 ms
execution-manager.js:878 Added test_idx=8 to multi-step action data for execution
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432948437
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
execution-manager.js:878 Added test_idx=8 to multi-step action data for execution
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432952712
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
execution-manager.js:878 Added test_idx=8 to multi-step action data for execution
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432956800
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
execution-manager.js:878 Added test_idx=8 to multi-step action data for execution
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432969767
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
execution-manager.js:878 Added test_idx=8 to multi-step action data for execution
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432972640
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432973143
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2768 [info] Executing action 516/641: Restart app: env[appid]
main.js?v=**********:2814 [Action Status] OR0SKKnFxy=running
execution-manager.js:3277 === EXECUTION MANAGER RECORDING STOP DEBUG ===
execution-manager.js:3278 Test case filename: App_Settings_AU_20250609145542.json
:8080/api/recording/stop_test_case:1  Failed to load resource: the server responded with a status of 500 (INTERNAL SERVER ERROR)
execution-manager.js:3292 Recording stop result: Object
execution-manager.js:3298 Failed to stop video recording for test case App_Settings_AU_20250609145542.json: No screen recording in progress
ExecutionManager._stopTestCaseRecording @ execution-manager.js:3298
execution-manager.js:3176 Updating TC State for testIdx 8: Old Status: running, New Status: passed, Error: null
execution-manager.js:636 Checking test case: AU - Performance
execution-manager.js:637 Test case header classes: Array(7)
execution-manager.js:638 Is already passed: false
execution-manager.js:3234 === EXECUTION MANAGER RECORDING START DEBUG ===
execution-manager.js:3235 Test case filename: temp_20250615085036.json
execution-manager.js:3236 Recording enabled: true
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
:8080/api/recording/start_test_case:1  Failed to load resource: the server responded with a status of 500 (INTERNAL SERVER ERROR)
execution-manager.js:3257 Recording start result: Object
execution-manager.js:3262 Failed to start video recording for test case temp_20250615085036.json: Screen recording failed: FFmpeg is required but not installed. Please install FFmpeg using "brew install ffmpeg" and try again.
ExecutionManager._startTestCaseRecording @ execution-manager.js:3262
execution-manager.js:683 Marked test case test-case-temp-20250615085036-json as current with test_idx=9
execution-manager.js:3176 Updating TC State for testIdx 9: Old Status: pending, New Status: running, Error: null
execution-manager.js:692 Will use test_idx=9 for actions in this test case
execution-manager.js:1122 Detected new test case at index 515. Previous test case: test-case-App-Settings-AU-20250609145542-json, Current test case: test-case-temp-20250615085036-json
execution-manager.js:1126 Adding a 10-second delay before starting a new test case at index 515
main.js?v=**********:5589 Test case "AU - Performance" expanded
execution-manager.js:243 Performing periodic Appium session health check
execution-manager.js:1131 Delay completed, continuing with test case at index 515
execution-manager.js:1137 Added test_idx=9 to action data for execution
execution-manager.js:65 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
execution-manager.js:1181 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 515, …}
main.js?v=**********:2814 [Action Status] OR0SKKnFxy=pass
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432992110
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing action 517/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
main.js?v=**********:2814 [Action Status] SqDiBhmyOG=running
execution-manager.js:1137 Added test_idx=9 to action data for execution
execution-manager.js:65 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
execution-manager.js:1181 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 516, …}
main.js?v=**********:2814 [Action Status] SqDiBhmyOG=pass
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753432997107
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing action 518/641: Tap if locator exists: xpath="//XCUIElementTypeStaticText[@name="Chat now"]/preceding-sibling::XCUIElementTypeImage[1]"
main.js?v=**********:2814 [Action Status] nPp27xJcCn=running
execution-manager.js:1137 Added test_idx=9 to action data for execution
execution-manager.js:65 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
execution-manager.js:1181 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 517, …}
main.js?v=**********:2814 [Action Status] nPp27xJcCn=pass
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433010175
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
main.js?v=**********:2768 [success] Screenshot refreshed successfully
main.js?v=**********:2768 [info] Executing action 519/641: Tap on Text: "Help"
main.js?v=**********:2814 [Action Status] g17Boaefhg=running
execution-manager.js:1137 Added test_idx=9 to action data for execution
execution-manager.js:65 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
execution-manager.js:1181 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 518, …}
main.js?v=**********:2814 [Action Status] g17Boaefhg=pass
main.js?v=**********:2201 Refreshing screenshot for device: ********-00186C801E13C01E
main.js?v=**********:2768 [info] Refreshing screenshot...
main.js?v=**********:2208 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433014799
main.js?v=**********:2768 [success] Screenshot refreshed
main.js?v=**********:2228 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 520/641: Tap on Text: "FAQ"
 [Action Status] DhFJzlme9K=running
 Added test_idx=9 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 519, …}
 [Action Status] DhFJzlme9K=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433019208
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 521/641: Swipe from (50%, 70%) to (50%, 30%)
 [Action Status] t6L5vWfBYM=running
 Added test_idx=9 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 520, …}
 [Action Status] t6L5vWfBYM=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433039511
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 522/641: Tap on Text: "click"
 [Action Status] I0tM87Yjhc=running
 Added test_idx=9 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 521, …}
 [Action Status] I0tM87Yjhc=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433044543
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 Performing periodic Appium session health check
 [info] Executing action 523/641: Tap on Text: "1800"
 [Action Status] MTRbUlaRvI=running
 Added test_idx=9 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 522, …}
 [Action Status] MTRbUlaRvI=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433049265
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 524/641: Tap on Text: "+61"
 [Action Status] RHEU77LRMw=running
 Added test_idx=9 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 523, …}
 [Action Status] RHEU77LRMw=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433053364
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 525/641: Launch app: env[appid]
 [Action Status] VqSa9z9R2Q=running
 Added test_idx=9 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 524, …}
 [Action Status] VqSa9z9R2Q=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433055333
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 526/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
 [Action Status] 7xs3GiydGF=running
 Added test_idx=9 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 525, …}
 [Action Status] 7xs3GiydGF=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433059749
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 527/641: Tap on Text: "Find"
 [Action Status] 6G6P3UE7Uy=running
 Added test_idx=9 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 526, …}
 [Action Status] 6G6P3UE7Uy=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433065436
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 528/641: iOS Function: text - Text: "kids toys"
 [Action Status] IL6kON0uQ9=running
 Added test_idx=9 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 527, …}
 [Action Status] IL6kON0uQ9=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433070022
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 529/641: Execute Test Case: Click_Paginations (8 steps)
 [Action Status] Z86xBjGUKY=running
 [info] Loading steps for multiStep action: Click_Paginations
 [info] Loaded 8 steps from test case: Click_Paginations
 [info] Executing Multi Step action step 1/8: Swipe from (50%, 80%) to (50%, 10%)
 Adding a 2-second delay before executing multi-step action for test_idx=9
 Delay completed, continuing with multi-step action for test_idx=9
 Added test_idx=9 to multi-step action data for execution
 Performing periodic Appium session health check
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433117891
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing Multi Step action step 2/8: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to next page"]
 Added test_idx=9 to multi-step action data for execution
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433136159
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing Multi Step action step 3/8: Swipe from (50%, 80%) to (50%, 10%)
 Added test_idx=9 to multi-step action data for execution
 Performing periodic Appium session health check
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433181292
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing Multi Step action step 4/8: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to next page"]
 Added test_idx=9 to multi-step action data for execution
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433199485
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing Multi Step action step 5/8: Swipe from (50%, 80%) to (50%, 10%)
 Added test_idx=9 to multi-step action data for execution
 Performing periodic Appium session health check
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433244133
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing Multi Step action step 6/8: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to previous page"]
 Added test_idx=9 to multi-step action data for execution
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433261846
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing Multi Step action step 7/8: Swipe from (50%, 80%) to (50%, 10%)
 Added test_idx=9 to multi-step action data for execution
 Performing periodic Appium session health check
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433306733
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing Multi Step action step 8/8: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to previous page"]
 Added test_idx=9 to multi-step action data for execution
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433324957
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433325459
 [success] Screenshot refreshed
 [info] Executing action 530/641: Restart app: env[appid]
 [Action Status] 5e4LeoW1YU=running
 Added test_idx=9 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 529, …}
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [Action Status] 5e4LeoW1YU=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433330294
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 531/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")]
 [Action Status] KyyS139agr=running
 Added test_idx=9 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 530, …}
 [Action Status] KyyS139agr=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433334857
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 532/641: Tap on Text: "Toys"
 [Action Status] zNRPvs2cC4=running
 Added test_idx=9 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 531, …}
 [Action Status] zNRPvs2cC4=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433339464
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 533/641: Tap on Text: "Age"
 [Action Status] eGQ7VrKUSo=running
 Added test_idx=9 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 532, …}
 [Action Status] eGQ7VrKUSo=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433343890
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 534/641: Tap on Text: "Months"
 [Action Status] dYEtjrv6lz=running
 Added test_idx=9 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 533, …}
 Performing periodic Appium session health check
 [Action Status] dYEtjrv6lz=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433348249
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 535/641: Swipe from (5%, 50%) to (90%, 50%)
 [Action Status] NkybTKfs2U=running
 Added test_idx=9 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 534, …}
 [Action Status] NkybTKfs2U=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433353814
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 536/641: Swipe from (5%, 50%) to (90%, 50%)
 [Action Status] To7bij5MnF=running
 Added test_idx=9 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 535, …}
 [Action Status] To7bij5MnF=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433359466
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 537/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
 [Action Status] WEB5St2Mb7=running
 Added test_idx=9 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 536, …}
 [Action Status] WEB5St2Mb7=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=*************
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 538/641: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
 [Action Status] SPE01N6pyp=running
 Added test_idx=9 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 537, …}
 [Action Status] SPE01N6pyp=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=*************
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 539/641: iOS Function: alert_accept
 [Action Status] kQJbqm7uCi=running
 Added test_idx=9 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 538, …}
 [Action Status] kQJbqm7uCi=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433373263
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 540/641: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
 [Action Status] TV4kJIIV9v=running
 Added test_idx=9 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 539, …}
 [Action Status] TV4kJIIV9v=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433377882
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 541/641: iOS Function: text - Text: "env[uname]"
 [Action Status] OUT2ASweb6=running
 Added test_idx=9 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 540, …}
 [Action Status] OUT2ASweb6=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433383045
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 542/641: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
 [Action Status] 7g2LmvjtEZ=running
 Added test_idx=9 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 541, …}
 [Action Status] 7g2LmvjtEZ=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433387669
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 543/641: iOS Function: text - Text: "env[pwd]"
 [Action Status] qkZ5KShdEU=running
 Added test_idx=9 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 542, …}
 [Action Status] qkZ5KShdEU=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433392703
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 544/641: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
 [Action Status] GTXmST3hEA=running
 Added test_idx=9 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 543, …}
 [Action Status] GTXmST3hEA=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433397423
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 545/641: Tap on Text: "Find"
 [Action Status] ewuLtuqVuo=running
 Added test_idx=9 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 544, …}
 [Action Status] ewuLtuqVuo=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433402818
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 546/641: iOS Function: text - Text: "env[cooker-id]"
 [Action Status] d40Oo7famr=running
 Added test_idx=9 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 545, …}
 Performing periodic Appium session health check
 [Action Status] d40Oo7famr=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433407355
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 547/641: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
 [Action Status] ksCBjJiwHZ=running
 Added test_idx=9 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 546, …}
 [Action Status] ksCBjJiwHZ=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433411036
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 548/641: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
 [Action Status] xmelRkcdVx=running
 Added test_idx=9 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 547, …}
 [Action Status] xmelRkcdVx=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433415367
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 549/641: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
 [Action Status] 0bnBNoqPt8=running
 Added test_idx=9 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 548, …}
 [Action Status] 0bnBNoqPt8=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433420207
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 550/641: Swipe from (50%, 70%) to (50%, 30%)
 [Action Status] ylslyLAYKb=running
 Added test_idx=9 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 549, …}
 [Action Status] ylslyLAYKb=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433437022
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 551/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")]
 [Action Status] Ef6OumM2eS=running
 Added test_idx=9 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 550, …}
 [error] Action 551 failed: Element not found or not tappable: xpath='//XCUIElementTypeButton[contains(@name,"to wishlist")]'
 [Action Status] Ef6OumM2eS=fail
 Finding hook actions for test case: test-case-temp-20250615085036-json
 Found 0 hook actions in test case test-case-temp-20250615085036-json
 _handleTestCaseFailure: Test case 9 failed. Current retries: 0, Max retries: 2
 [info] === RETRYING TEST CASE: temp_20250615085036.json (Attempt 2 of 3) ===
 Updating TC State for testIdx 9: Old Status: running, New Status: rerunning, Error: Retrying after failure at action 550
 [info] Executing action 516/641: Restart app: env[appid]
 [Action Status] OR0SKKnFxy=running
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 515, …}
 [Action Status] OR0SKKnFxy=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433458220
 [success] Screenshot refreshed
 [info] Executing action 517/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
 [Action Status] SqDiBhmyOG=running
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 516, …}
 [Action Status] SqDiBhmyOG=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433462659
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 518/641: Tap if locator exists: xpath="//XCUIElementTypeStaticText[@name="Chat now"]/preceding-sibling::XCUIElementTypeImage[1]"
 [Action Status] nPp27xJcCn=running
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 517, …}
 Performing periodic Appium session health check
 [Action Status] nPp27xJcCn=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433475737
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 519/641: Tap on Text: "Help"
 [Action Status] g17Boaefhg=running
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 518, …}
 [Action Status] g17Boaefhg=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433491187
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 520/641: Tap on Text: "FAQ"
 [Action Status] DhFJzlme9K=running
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 519, …}
 [Action Status] DhFJzlme9K=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433501912
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 521/641: Swipe from (50%, 70%) to (50%, 30%)
 [Action Status] t6L5vWfBYM=running
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 520, …}
 Performing periodic Appium session health check
 [Action Status] t6L5vWfBYM=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433531212
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 522/641: Tap on Text: "click"
 [Action Status] I0tM87Yjhc=running
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 521, …}
 [Action Status] I0tM87Yjhc=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433536094
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 523/641: Tap on Text: "1800"
 [Action Status] MTRbUlaRvI=running
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 522, …}
 [Action Status] MTRbUlaRvI=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433540750
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 524/641: Tap on Text: "+61"
 [Action Status] RHEU77LRMw=running
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 523, …}
 [Action Status] RHEU77LRMw=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433544716
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 525/641: Launch app: env[appid]
 [Action Status] VqSa9z9R2Q=running
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 524, …}
 [Action Status] VqSa9z9R2Q=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433546688
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 526/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
 [Action Status] 7xs3GiydGF=running
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 525, …}
 [Action Status] 7xs3GiydGF=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433551049
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 527/641: Tap on Text: "Find"
 [Action Status] 6G6P3UE7Uy=running
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 526, …}
 Performing periodic Appium session health check
 [error] Action 527 failed: Text 'Find' not found within timeout (30s)
 [Action Status] 6G6P3UE7Uy=fail
 Finding hook actions for test case: test-case-temp-20250615085036-json
 Found 0 hook actions in test case test-case-temp-20250615085036-json
 _handleTestCaseFailure: Test case 9 failed. Current retries: 1, Max retries: 2
 [info] === RETRYING TEST CASE: temp_20250615085036.json (Attempt 3 of 3) ===
 Updating TC State for testIdx 9: Old Status: rerunning, New Status: rerunning, Error: Retrying after failure at action 526
 [info] Executing action 516/641: Restart app: env[appid]
 [Action Status] OR0SKKnFxy=running
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 515, …}
 [Action Status] OR0SKKnFxy=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433592135
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 517/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
 [Action Status] SqDiBhmyOG=running
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 516, …}
 [Action Status] SqDiBhmyOG=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433597127
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 518/641: Tap if locator exists: xpath="//XCUIElementTypeStaticText[@name="Chat now"]/preceding-sibling::XCUIElementTypeImage[1]"
 [Action Status] nPp27xJcCn=running
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 517, …}
 [Action Status] nPp27xJcCn=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433610213
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 519/641: Tap on Text: "Help"
 [Action Status] g17Boaefhg=running
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 518, …}
 [Action Status] g17Boaefhg=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433614739
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 520/641: Tap on Text: "FAQ"
 [Action Status] DhFJzlme9K=running
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 519, …}
 [Action Status] DhFJzlme9K=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433619115
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 521/641: Swipe from (50%, 70%) to (50%, 30%)
 [Action Status] t6L5vWfBYM=running
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 520, …}
 [Action Status] t6L5vWfBYM=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433639525
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 522/641: Tap on Text: "click"
 [Action Status] I0tM87Yjhc=running
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 521, …}
 [Action Status] I0tM87Yjhc=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433644422
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 523/641: Tap on Text: "1800"
 [Action Status] MTRbUlaRvI=running
 Performing periodic Appium session health check
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 522, …}
 [Action Status] MTRbUlaRvI=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433649068
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 524/641: Tap on Text: "+61"
 [Action Status] RHEU77LRMw=running
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 523, …}
 [Action Status] RHEU77LRMw=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433652977
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 525/641: Launch app: env[appid]
 [Action Status] VqSa9z9R2Q=running
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 524, …}
 [Action Status] VqSa9z9R2Q=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433654953
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 526/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
 [Action Status] 7xs3GiydGF=running
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 525, …}
 [Action Status] 7xs3GiydGF=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433659335
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 527/641: Tap on Text: "Find"
 [Action Status] 6G6P3UE7Uy=running
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 526, …}
 [Action Status] 6G6P3UE7Uy=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433664827
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 528/641: iOS Function: text - Text: "kids toys"
 [Action Status] IL6kON0uQ9=running
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 527, …}
 [Action Status] IL6kON0uQ9=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433669410
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 529/641: Execute Test Case: Click_Paginations (8 steps)
 [Action Status] Z86xBjGUKY=running
 [info] Loading steps for multiStep action: Click_Paginations
 [info] Loaded 8 steps from test case: Click_Paginations
 [info] Executing Multi Step action step 1/8: Swipe from (50%, 80%) to (50%, 10%)
 Adding a 2-second delay before executing multi-step action for test_idx=9
 Delay completed, continuing with multi-step action for test_idx=9
 Added test_idx=9 to multi-step action data for execution
 Updating TC State for testIdx 9: Old Status: rerunning, New Status: running, Error: null
 Performing periodic Appium session health check
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433716569
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing Multi Step action step 2/8: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to next page"]
 Added test_idx=9 to multi-step action data for execution
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433735501
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing Multi Step action step 3/8: Swipe from (50%, 80%) to (50%, 10%)
 Added test_idx=9 to multi-step action data for execution
 Performing periodic Appium session health check
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433780413
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing Multi Step action step 4/8: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to next page"]
 Added test_idx=9 to multi-step action data for execution
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433798622
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing Multi Step action step 5/8: Swipe from (50%, 80%) to (50%, 10%)
 Added test_idx=9 to multi-step action data for execution
 Performing periodic Appium session health check
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433843273
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing Multi Step action step 6/8: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to previous page"]
 Added test_idx=9 to multi-step action data for execution
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433861731
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing Multi Step action step 7/8: Swipe from (50%, 80%) to (50%, 10%)
 Added test_idx=9 to multi-step action data for execution
 Performing periodic Appium session health check
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433906684
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing Multi Step action step 8/8: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to previous page"]
 Added test_idx=9 to multi-step action data for execution
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433924961
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433925465
 [success] Screenshot refreshed
 [info] Executing action 530/641: Restart app: env[appid]
 [Action Status] 5e4LeoW1YU=running
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 529, …}
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [Action Status] 5e4LeoW1YU=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433930370
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 531/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")]
 [Action Status] KyyS139agr=running
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 530, …}
 [Action Status] KyyS139agr=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433934783
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 532/641: Tap on Text: "Toys"
 [Action Status] zNRPvs2cC4=running
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 531, …}
 [Action Status] zNRPvs2cC4=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433939203
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 533/641: Tap on Text: "Age"
 [Action Status] eGQ7VrKUSo=running
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 532, …}
 [Action Status] eGQ7VrKUSo=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433943841
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 534/641: Tap on Text: "Months"
 [Action Status] dYEtjrv6lz=running
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 533, …}
 Performing periodic Appium session health check
 [Action Status] dYEtjrv6lz=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433948338
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 535/641: Swipe from (5%, 50%) to (90%, 50%)
 [Action Status] NkybTKfs2U=running
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 534, …}
 [Action Status] NkybTKfs2U=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433954292
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 536/641: Swipe from (5%, 50%) to (90%, 50%)
 [Action Status] To7bij5MnF=running
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 535, …}
 [Action Status] To7bij5MnF=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433959936
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 537/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
 [Action Status] WEB5St2Mb7=running
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 536, …}
 [Action Status] WEB5St2Mb7=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=*************
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 538/641: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
 [Action Status] SPE01N6pyp=running
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 537, …}
 [Action Status] SPE01N6pyp=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=*************
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 539/641: iOS Function: alert_accept
 [Action Status] kQJbqm7uCi=running
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 538, …}
 [Action Status] kQJbqm7uCi=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433973703
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 540/641: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
 [Action Status] TV4kJIIV9v=running
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 539, …}
 [Action Status] TV4kJIIV9v=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433978281
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 541/641: iOS Function: text - Text: "env[uname]"
 [Action Status] OUT2ASweb6=running
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 540, …}
 [Action Status] OUT2ASweb6=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433983457
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 542/641: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
 [Action Status] 7g2LmvjtEZ=running
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 541, …}
 [Action Status] 7g2LmvjtEZ=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433988064
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 543/641: iOS Function: text - Text: "env[pwd]"
 [Action Status] qkZ5KShdEU=running
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 542, …}
 [Action Status] qkZ5KShdEU=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433993014
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 544/641: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
 [Action Status] GTXmST3hEA=running
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 543, …}
 [Action Status] GTXmST3hEA=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753433996483
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 545/641: Tap on Text: "Find"
 [Action Status] ewuLtuqVuo=running
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 544, …}
 [Action Status] ewuLtuqVuo=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434001893
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 546/641: iOS Function: text - Text: "env[cooker-id]"
 [Action Status] d40Oo7famr=running
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 545, …}
 Performing periodic Appium session health check
 [Action Status] d40Oo7famr=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434006676
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 547/641: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
 [Action Status] ksCBjJiwHZ=running
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 546, …}
 [Action Status] ksCBjJiwHZ=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434010490
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 548/641: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
 [Action Status] xmelRkcdVx=running
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 547, …}
 [Action Status] xmelRkcdVx=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434014928
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 549/641: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
 [Action Status] 0bnBNoqPt8=running
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 548, …}
 [Action Status] 0bnBNoqPt8=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434019669
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 550/641: Swipe from (50%, 70%) to (50%, 30%)
 [Action Status] ylslyLAYKb=running
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 549, …}
 [Action Status] ylslyLAYKb=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434026468
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 551/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")]
 [Action Status] Ef6OumM2eS=running
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 550, …}
 [error] Action 551 failed: Element not found or not tappable: xpath='//XCUIElementTypeButton[contains(@name,"to wishlist")]'
 [Action Status] Ef6OumM2eS=fail
 Finding hook actions for test case: test-case-temp-20250615085036-json
 Found 0 hook actions in test case test-case-temp-20250615085036-json
 _handleTestCaseFailure: Test case 9 failed. Current retries: 2, Max retries: 2
 _handleTestCaseFailure: No more retries available for test case 9, marking as permanently failed
 Updating TC State for testIdx 9: Old Status: running, New Status: failed, Error: Failed after 2 retry attempts
 _skipToNextTestCase: Found cleanup step at index 583, will not skip
 [info] Skipping remaining steps in failed test case (moving from action 551 to 583), but preserving cleanup steps
 [info] Executing action 584/641: cleanupSteps action
 [Action Status] Ll4UlkE3L9=running
 [info] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
 [info] Loaded 6 steps from test case: Kmart_AU_Cleanup
 [info] Executing Multi Step action step 1/6: Restart app: au.com.kmart
 Adding a 2-second delay before executing multi-step action for test_idx=9
 Delay completed, continuing with multi-step action for test_idx=9
 Added test_idx=9 to multi-step action data for execution
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434047837
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing Multi Step action step 2/6: Wait for 5 ms
 Added test_idx=9 to multi-step action data for execution
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434054754
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
 Added test_idx=9 to multi-step action data for execution
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434059056
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
 Added test_idx=9 to multi-step action data for execution
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434063169
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
 Added test_idx=9 to multi-step action data for execution
 Performing periodic Appium session health check
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434068582
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
 Added test_idx=9 to multi-step action data for execution
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434071560
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434072063
 [success] Screenshot refreshed
 [info] Executing action 585/641: Terminate app: au.com.kmart
 [Action Status] K8uGC1LDOS=running
 === EXECUTION MANAGER RECORDING STOP DEBUG ===
 Test case filename: temp_20250615085036.json
  POST http://localhost:8080/api/recording/stop_test_case 500 (INTERNAL SERVER ERROR)
ExecutionManager._stopTestCaseRecording @ execution-manager.js:3281
executeWithConfiguration @ execution-manager.js:611
 Recording stop result: {message: 'No screen recording in progress', status: 'warning'}
 Failed to stop video recording for test case temp_20250615085036.json: No screen recording in progress
ExecutionManager._stopTestCaseRecording @ execution-manager.js:3298
await in ExecutionManager._stopTestCaseRecording
executeWithConfiguration @ execution-manager.js:611
 Checking test case: All Payments Check
 Test case header classes: (7) ['list-group-item', 'test-case-header', 'bg-light', 'd-flex', 'justify-content-between', 'align-items-center', 'collapsed']
 Is already passed: false
 === EXECUTION MANAGER RECORDING START DEBUG ===
 Test case filename: All_Payments_Check_20250512194232.json
 Recording enabled: true
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
  POST http://localhost:8080/api/recording/start_test_case 500 (INTERNAL SERVER ERROR)
ExecutionManager._startTestCaseRecording @ execution-manager.js:3248
executeWithConfiguration @ execution-manager.js:671
 Recording start result: {message: 'Screen recording failed: FFmpeg is required but no…FFmpeg using "brew install ffmpeg" and try again.', status: 'error'}
 Failed to start video recording for test case All_Payments_Check_20250512194232.json: Screen recording failed: FFmpeg is required but not installed. Please install FFmpeg using "brew install ffmpeg" and try again.
ExecutionManager._startTestCaseRecording @ execution-manager.js:3262
await in ExecutionManager._startTestCaseRecording
executeWithConfiguration @ execution-manager.js:671
 Marked test case test-case-All-Payments-Check-20250512194232-json as current with test_idx=10
 Updating TC State for testIdx 10: Old Status: pending, New Status: running, Error: null
 Will use test_idx=10 for actions in this test case
 Detected new test case at index 584. Previous test case: test-case-temp-20250615085036-json, Current test case: test-case-All-Payments-Check-20250512194232-json
 Adding a 10-second delay before starting a new test case at index 584
 Test case "All Payments Check" expanded
 Delay completed, continuing with test case at index 584
 Added test_idx=10 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 584, …}
 [Action Status] K8uGC1LDOS=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434088823
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 586/641: Restart app: au.com.kmart
 [Action Status] 3uORTsBIAg=running
 Added test_idx=10 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 585, …}
 [Action Status] 3uORTsBIAg=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=*************
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 587/641: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
 [Action Status] MxtVneSHFi=running
 Added test_idx=10 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 586, …}
 [Action Status] MxtVneSHFi=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=*************
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 588/641: iOS Function: alert_accept
 [Action Status] TrbMRAIV8i=running
 Added test_idx=10 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 587, …}
 [Action Status] TrbMRAIV8i=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434104252
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 589/641: Execute Test Case: Kmart-Signin (5 steps)
 [Action Status] JEpLBji8jZ=running
 [info] Loading steps for multiStep action: Kmart-Signin
 [info] Loaded 5 steps from test case: Kmart-Signin
 [info] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
 Adding a 2-second delay before executing multi-step action for test_idx=10
 Delay completed, continuing with multi-step action for test_idx=10
 Added test_idx=10 to multi-step action data for execution
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434110463
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
 Added test_idx=10 to multi-step action data for execution
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434114932
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing Multi Step action step 3/5: iOS Function: text - Text: "<EMAIL>"
 Added test_idx=10 to multi-step action data for execution
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434119949
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
 Added test_idx=10 to multi-step action data for execution
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434124485
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 Performing periodic Appium session health check
 [info] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
 Added test_idx=10 to multi-step action data for execution
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434129379
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434129881
 [success] Screenshot refreshed
 [info] Executing action 590/641: Wait till xpath=//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]
 [Action Status] ACaNCAo69V=running
 Added test_idx=10 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 589, …}
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [Action Status] ACaNCAo69V=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434134794
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 591/641: Tap on Text: "Find"
 [Action Status] 91WZz4k3NI=running
 Added test_idx=10 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 590, …}
 [Action Status] 91WZz4k3NI=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434140189
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 592/641: iOS Function: text - Text: "P_42691341"
 [Action Status] GPTMDcrFC2=running
 Added test_idx=10 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 591, …}
 [Action Status] GPTMDcrFC2=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434144792
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 593/641: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
 [Action Status] FSM5PqLDko=running
 Added test_idx=10 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 592, …}
 [Action Status] FSM5PqLDko=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434148620
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 594/641: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
 [Action Status] 6GkdPPZo8e=running
 Added test_idx=10 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 593, …}
 [Action Status] 6GkdPPZo8e=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434152967
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 595/641: Swipe from (50%, 70%) to (50%, 50%)
 [Action Status] PzxTDnwsZ7=running
 Added test_idx=10 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 594, …}
 [Action Status] PzxTDnwsZ7=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434158564
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 596/641: Tap on element with accessibility_id: Add to bag
 [Action Status] VLrfDHfkI8=running
 Added test_idx=10 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 595, …}
 [Action Status] VLrfDHfkI8=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434166821
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 597/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
 [Action Status] gPYNwJ0HKo=running
 Added test_idx=10 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 596, …}
 [Action Status] gPYNwJ0HKo=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434171893
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 598/641: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
 [Action Status] E3RDcrIH6J=running
 Added test_idx=10 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 597, …}
 [Action Status] E3RDcrIH6J=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434184500
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 Performing periodic Appium session health check
 [info] Executing action 599/641: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
 [Action Status] aqBkqyVhrZ=running
 Added test_idx=10 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 598, …}
 [Action Status] aqBkqyVhrZ=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434188392
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 600/641: Tap on element with xpath: //XCUIElementTypeButton[@name="Delivery"]
 [Action Status] hwdyCKFAUJ=running
 Added test_idx=10 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 599, …}
 [Action Status] hwdyCKFAUJ=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434192802
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 601/641: Swipe up till element xpath: "//XCUIElementTypeButton[@name="Continue to details"]" is visible
 [Action Status] xAa049Qgls=running
 Added test_idx=10 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 600, …}
 [Action Status] xAa049Qgls=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434204184
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 602/641: Tap on element with xpath: //XCUIElementTypeButton[@name="Continue to details"]
 [Action Status] Q5A0cNaJ24=running
 Added test_idx=10 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 601, …}
 [Action Status] Q5A0cNaJ24=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434208592
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 603/641: Tap on element with xpath: //XCUIElementTypeTextField[@name="First Name"]
 [Action Status] h9trcMrvxt=running
 Added test_idx=10 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 602, …}
 [Action Status] h9trcMrvxt=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434212858
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 604/641: textClear action
 [Action Status] CLMmkV1OIM=running
 Added test_idx=10 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 603, …}
 [Action Status] CLMmkV1OIM=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434219841
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 605/641: Tap on element with xpath: //XCUIElementTypeTextField[@name="Last Name"]
 [Action Status] p8rfQL9ara=running
 Added test_idx=10 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 604, …}
 [Action Status] p8rfQL9ara=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434224255
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 606/641: textClear action
 [Action Status] QvuueoTR8W=running
 Added test_idx=10 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 605, …}
 [Action Status] QvuueoTR8W=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434231202
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 607/641: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
 [Action Status] 9B5MQGTmpP=running
 Added test_idx=10 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 606, …}
 [Action Status] 9B5MQGTmpP=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434235557
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 608/641: textClear action
 [Action Status] lWJtKSqlPS=running
 Added test_idx=10 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 607, …}
 [Action Status] lWJtKSqlPS=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434243346
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 609/641: Tap on element with xpath: //XCUIElementTypeTextField[@name="Mobile number"]
 [Action Status] yi5EsHEFvc=running
 Added test_idx=10 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 608, …}
 Performing periodic Appium session health check
 [Action Status] yi5EsHEFvc=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434247747
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 610/641: textClear action
 [Action Status] SFj4Aa7RHQ=running
 Added test_idx=10 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 609, …}
 [Action Status] SFj4Aa7RHQ=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434254869
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 611/641: iOS Function: text - Text: " "
 [Action Status] kDpsm2D3xt=running
 Added test_idx=10 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 610, …}
 [Action Status] kDpsm2D3xt=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434259362
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 612/641: Tap on Text: "address"
 [Action Status] 5ZzW1VVSzy=running
 Added test_idx=10 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 611, …}
 [Action Status] 5ZzW1VVSzy=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434264573
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 613/641: Tap and Type at (54, 304): "305 238 Flinders"
 [Action Status] SQ1i1ElZCE=running
 Added test_idx=10 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 612, …}
 [Action Status] SQ1i1ElZCE=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434271908
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 614/641: Tap on element with xpath: //XCUIElementTypeButton[@name="Done"]
 [Action Status] NcU6aex76k=running
 Added test_idx=10 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 613, …}
 [Action Status] NcU6aex76k=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434276359
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 615/641: Tap on image: env[delivery-address-img]
 [Action Status] mMnRNh3NEd=running
 Added test_idx=10 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 614, …}
 [Action Status] mMnRNh3NEd=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434280508
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 616/641: Swipe up till element xpath: "//XCUIElementTypeButton[@name="Continue to payment"]" is visible
 [Action Status] TTpwkHEyuE=running
 Added test_idx=10 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 615, …}
 [Action Status] TTpwkHEyuE=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434293745
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 617/641: Tap on element with xpath: //XCUIElementTypeButton[@name="Continue to payment"]
 [Action Status] 1Lirmyxkft=running
 Added test_idx=10 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 616, …}
 [Action Status] 1Lirmyxkft=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434298096
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 618/641: Tap on element with xpath: //XCUIElementTypeOther[@name="Select a payment method"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[2]/XCUIElementTypeOther
 [Action Status] 6LQ5cq0f6N=running
 Added test_idx=10 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 617, …}
 [Action Status] 6LQ5cq0f6N=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434302594
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 619/641: Swipe up till element xpath: "//XCUIElementTypeLink[@name="PayPal"]" is visible
 [Action Status] CBBib3pFkq=running
 Added test_idx=10 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 618, …}
 Performing periodic Appium session health check
 [Action Status] CBBib3pFkq=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434310968
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 620/641: Tap on element with xpath: //XCUIElementTypeLink[@name="PayPal"]
 [Action Status] ftA0OJvd0W=running
 Added test_idx=10 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 619, …}
 [Action Status] ftA0OJvd0W=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434315332
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 621/641: Check if element with xpath="//XCUIElementTypeStaticText[contains(@name,"PayPal")]" exists
 [Action Status] mfOWujfRpL=running
 Added test_idx=10 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 620, …}
 [Action Status] mfOWujfRpL=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434319009
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 622/641: Tap on element with xpath: //XCUIElementTypeButton[@name="close"]
 [Action Status] XLpUP3Wr93=running
 Added test_idx=10 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 621, …}
 [Action Status] XLpUP3Wr93=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434323448
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 623/641: Tap on element with xpath: //XCUIElementTypeOther[@name="Select a payment method"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[3]/XCUIElementTypeOther
 [Action Status] dkSs61jGvX=running
 Added test_idx=10 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 622, …}
 [Action Status] dkSs61jGvX=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434327795
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 624/641: Tap on element with xpath: //XCUIElementTypeLink[@name="Pay in 4"]
 [Action Status] GN587Y6VBQ=running
 Added test_idx=10 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 623, …}
 [Action Status] GN587Y6VBQ=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434332186
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 625/641: Check if element with xpath="//XCUIElementTypeStaticText[@name="Pay in 4 with PayPal"]" exists
 [Action Status] wSdfNe4Kww=running
 Added test_idx=10 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 624, …}
 [Action Status] wSdfNe4Kww=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434336019
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 626/641: Tap on element with xpath: //XCUIElementTypeButton[@name="close"]
 [Action Status] TzPItWbvDR=running
 Added test_idx=10 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 625, …}
 [Action Status] TzPItWbvDR=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434340592
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 627/641: Tap on element with xpath: //XCUIElementTypeOther[@name="Select a payment method"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[4]/XCUIElementTypeOther
 [Action Status] YBT2MVclAv=running
 Added test_idx=10 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 626, …}
 [Action Status] YBT2MVclAv=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434345060
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 628/641: Tap on element with xpath: //XCUIElementTypeButton[@name="Afterpay, available on orders between $70-$2,000."]
 [Action Status] 9Pwdq32eUk=running
 Added test_idx=10 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 627, …}
 [Action Status] 9Pwdq32eUk=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434349428
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 629/641: Check if element with xpath="//XCUIElementTypeImage[@name="Afterpay"]" exists
 [Action Status] lSG7un0qKK=running
 Added test_idx=10 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 628, …}
 [Action Status] lSG7un0qKK=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434354867
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 630/641: Tap on image: banner-close-updated.png
 [Action Status] vYLhraWpQm=running
 Added test_idx=10 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 629, …}
 [Action Status] vYLhraWpQm=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434359118
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 631/641: Tap on element with xpath: //XCUIElementTypeOther[@name="Select a payment method"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[5]/XCUIElementTypeOther
 [Action Status] YqmO7h7VP0=running
 Added test_idx=10 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 630, …}
 [Action Status] YqmO7h7VP0=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434363494
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 632/641: Tap on element with xpath: //XCUIElementTypeButton[@name="Afterpay, available on orders between $70-$2,000."]
 [Action Status] UgjXUTZy7Z=running
 Added test_idx=10 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 631, …}
 Performing periodic Appium session health check
 [Action Status] UgjXUTZy7Z=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=*************
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 633/641: Check if element with xpath="//XCUIElementTypeStaticText[@name="Sign in with your Zip account"]" exists
 [Action Status] TAKgcEDqvz=running
 Added test_idx=10 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 632, …}
 [Action Status] TAKgcEDqvz=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=*************
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 634/641: Tap on image: banner-close-updated.png
 [Action Status] vYLhraWpQm=running
 Added test_idx=10 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 633, …}
 [Action Status] vYLhraWpQm=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434375466
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 635/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
 [Action Status] gPYNwJ0HKo=running
 Added test_idx=10 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 634, …}
 [Action Status] gPYNwJ0HKo=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434380576
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 636/641: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
 [Action Status] wSHsGWAwPm=running
 Added test_idx=10 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 635, …}
 [Action Status] wSHsGWAwPm=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434393060
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 637/641: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
 [Action Status] aqBkqyVhrZ=running
 Added test_idx=10 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 636, …}
 [Action Status] aqBkqyVhrZ=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434396910
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 638/641: Swipe up till element xpath: "//XCUIElementTypeButton[contains(@name,"Remove")]" is visible
 [Action Status] 2bcxKJ2cPg=running
 Added test_idx=10 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 637, …}
 [Action Status] 2bcxKJ2cPg=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434403814
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 639/641: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
 [Action Status] a4pJa7EAyI=running
 Added test_idx=10 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 638, …}
 [Action Status] a4pJa7EAyI=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434408220
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 640/641: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Continue shopping"]
 [Action Status] q6kSH9e0MI=running
 Added test_idx=10 to action data for execution
 Execution context loaded: {execution_id: null, is_test_suite_execution: false, suite_id: null, test_case_name: null, test_idx: null}
 Executing action with parameters: {action: {…}, force_screenshot: true, record_execution: 'YES', retry_failed_tests: 2, action_index: 639, …}
 [Action Status] q6kSH9e0MI=pass
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434412470
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 641/641: Execute Test Case: Kmart_AU_Cleanup (6 steps)
 [Action Status] 4pFqgUdIwt=running
 [info] Loading steps for multiStep action: Kmart_AU_Cleanup
 [info] Loaded 6 steps from test case: Kmart_AU_Cleanup
 [info] Executing Multi Step action step 1/6: Restart app: au.com.kmart
 Adding a 2-second delay before executing multi-step action for test_idx=10
 Delay completed, continuing with multi-step action for test_idx=10
 Added test_idx=10 to multi-step action data for execution
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434419927
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing Multi Step action step 2/6: Wait for 5 ms
 Added test_idx=10 to multi-step action data for execution
 Performing periodic Appium session health check
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434426874
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
 Added test_idx=10 to multi-step action data for execution
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434431315
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
 Added test_idx=10 to multi-step action data for execution
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434435341
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
 Added test_idx=10 to multi-step action data for execution
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434440746
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
 Added test_idx=10 to multi-step action data for execution
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434443688
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 Refreshing screenshot for device: ********-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1753429273190_0f6318efw_1753429273190_0f6318efw&t=1753434444190
 [success] Screenshot refreshed
 === EXECUTION MANAGER RECORDING STOP DEBUG ===
 Test case filename: All_Payments_Check_20250512194232.json
  POST http://localhost:8080/api/recording/stop_test_case 500 (INTERNAL SERVER ERROR)
ExecutionManager._stopTestCaseRecording @ execution-manager.js:3281
executeWithConfiguration @ execution-manager.js:1686
 Recording stop result: {message: 'No screen recording in progress', status: 'warning'}
 Failed to stop video recording for test case All_Payments_Check_20250512194232.json: No screen recording in progress
ExecutionManager._stopTestCaseRecording @ execution-manager.js:3298
await in ExecutionManager._stopTestCaseRecording
executeWithConfiguration @ execution-manager.js:1686
 Test case states BEFORE finalization: (11) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
 Test case 0 status unchanged: passed
 Test case 1 status unchanged: passed
 Test case 2 status unchanged: passed
 Test case 3 status unchanged: passed
 Test case 4 has UI errors, marking as FAILED
 Updating TC State for testIdx 4: Old Status: failed, New Status: failed, Error: UI elements indicate failure
 Test case 5 status unchanged: passed
 Test case 6 status unchanged: passed
 Test case 7 status unchanged: passed
 Test case 8 status unchanged: passed
 Test case 9 has UI errors, marking as FAILED
 Updating TC State for testIdx 9: Old Status: failed, New Status: failed, Error: UI elements indicate failure
 Test case 10 finalized from 'running' to 'passed'
 Updating TC State for testIdx 10: Old Status: running, New Status: passed, Error: null
 Test case states AFTER finalization: (11) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
 [executeAllActions] Test Case States: (11) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
 [executeAllActions] lastExecutionHadFailures: true
 [warning] 2 tests failed.
 2 tests failed (from internal state)
 Stop buttons hidden
 [_updateUIForStop] Test Case States before Rerun Button Check: (11) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
 [_updateUIForStop] lastExecutionHadFailures: true
 Stopped periodic Appium session health checks
 Export Manager: Execution completed event received
 Export Manager: Setting isExecuting to false and checking for latest report
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: null
 Export Manager: Button state updated:
   - shouldEnable: null
   - isExecuting: false
   - latestReportId: null
   - wasDisabled: true
   - nowDisabled: true
   - button element: 
 [info] Generating execution report...
 [info] Saving 4685 action log entries to file...
 [error] Execution failed but report was generated.
 [success] Action logs saved successfully
 Test case "AU - Performance" collapsed
 Test case "All Payments Check" collapsed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 Export Manager: First check (2s) - checking for latest report...
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: true
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Second check (5s) - checking for latest report...
 Export Manager: Checking for latest report...
 Export Manager: Third check (5s) - checking for latest report...
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Third check (8s) - checking for latest report...
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 [success] Execution report generated successfully
 [info] Cleaning up screenshots...
 [success] All screenshots deleted successfully
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 ExecutionManager: Dispatching execution-completed event after report generation
 Export Manager: Execution completed event received
 Export Manager: Setting isExecuting to false and checking for latest report
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: First check (2s) - checking for latest report...
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Second check (5s) - checking for latest report...
 Export Manager: Checking for latest report...
 Export Manager: Third check (5s) - checking for latest report...
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Third check (8s) - checking for latest report...
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Retry button clicked for test case: Kmart-Prod-Signin (File: KmartProdSignin_20250426221008.json)
 Stop buttons shown for test suite execution
 [info] Executing action: Restart app: env[appid]
 Test case "Kmart-Prod-Signin" expanded
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250725_174204/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250725_174204
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250725_174204
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250725_174204
   - isExecuting: false
   - latestReportId: testsuite_execution_20250725_174204
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
