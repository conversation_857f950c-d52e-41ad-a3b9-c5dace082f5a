#!/usr/bin/env python3
"""
Debug script to monitor retry functionality in real-time
"""
import sqlite3
import time
import os
import json
from datetime import datetime

def get_db_path():
    """Get the database path for iOS (port 8080)"""
    return "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/data/test_execution.db"

def monitor_execution_tracking():
    """Monitor the execution_tracking table for retry attempts"""
    db_path = get_db_path()
    
    if not os.path.exists(db_path):
        print(f"❌ Database not found at: {db_path}")
        return
    
    print(f"🔍 Monitoring execution tracking database: {db_path}")
    print("=" * 80)
    
    last_count = 0
    
    while True:
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # Get current count of entries
            cursor.execute("SELECT COUNT(*) FROM execution_tracking")
            current_count = cursor.fetchone()[0]
            
            if current_count > last_count:
                print(f"\n📊 New entries detected! Total entries: {current_count}")
                
                # Get the latest entries
                cursor.execute("""
                    SELECT id, suite_id, test_idx, filename, status, retry_count, max_retries, 
                           last_error, start_time, in_progress
                    FROM execution_tracking 
                    ORDER BY id DESC 
                    LIMIT 10
                """)
                
                entries = cursor.fetchall()
                
                print("\n🔄 Latest Execution Tracking Entries:")
                print("-" * 120)
                print(f"{'ID':<4} {'Suite':<12} {'Test':<4} {'File':<25} {'Status':<10} {'Retry':<8} {'Error':<30} {'Time':<20}")
                print("-" * 120)
                
                for entry in entries:
                    id_val, suite_id, test_idx, filename, status, retry_count, max_retries, error, start_time, in_progress = entry
                    retry_info = f"{retry_count}/{max_retries}"
                    error_short = (error[:27] + "...") if error and len(error) > 30 else (error or "")
                    filename_short = (filename[:22] + "...") if filename and len(filename) > 25 else (filename or "")
                    
                    print(f"{id_val:<4} {suite_id:<12} {test_idx:<4} {filename_short:<25} {status:<10} {retry_info:<8} {error_short:<30} {start_time:<20}")
                
                # Check for retry patterns
                cursor.execute("""
                    SELECT filename, retry_count, max_retries, status, COUNT(*) as count
                    FROM execution_tracking 
                    WHERE retry_count > 0 OR max_retries > 0
                    GROUP BY filename, retry_count, max_retries, status
                    ORDER BY filename, retry_count
                """)
                
                retry_patterns = cursor.fetchall()
                
                if retry_patterns:
                    print("\n🔁 Retry Patterns Detected:")
                    print("-" * 80)
                    for pattern in retry_patterns:
                        filename, retry_count, max_retries, status, count = pattern
                        print(f"  📁 {filename}: {retry_count}/{max_retries} retries, status={status}, count={count}")
                
                last_count = current_count
            
            conn.close()
            
            # Check for test suite execution logs
            print(f"\r⏰ Monitoring... (Entries: {current_count}) - {datetime.now().strftime('%H:%M:%S')}", end="", flush=True)
            
            time.sleep(2)
            
        except KeyboardInterrupt:
            print("\n\n🛑 Monitoring stopped by user")
            break
        except Exception as e:
            print(f"\n❌ Error monitoring database: {e}")
            time.sleep(5)

def check_frontend_backend_flow():
    """Check if retry parameters are being passed correctly"""
    print("\n🔍 Checking Frontend-Backend Parameter Flow:")
    print("=" * 60)
    
    # Check the execution manager JavaScript
    js_file = "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/js/execution-manager.js"
    
    if os.path.exists(js_file):
        with open(js_file, 'r') as f:
            content = f.read()
            
        # Look for retry parameter handling
        if 'retryFailedTests' in content:
            print("✅ Found retryFailedTests parameter in execution-manager.js")
        else:
            print("❌ retryFailedTests parameter NOT found in execution-manager.js")
            
        if 'retry_failed_tests' in content:
            print("✅ Found retry_failed_tests parameter in execution-manager.js")
        else:
            print("❌ retry_failed_tests parameter NOT found in execution-manager.js")
    else:
        print(f"❌ execution-manager.js not found at: {js_file}")

def analyze_player_retry_logic():
    """Analyze the Player class retry logic"""
    print("\n🔍 Analyzing Player Retry Logic:")
    print("=" * 50)
    
    player_file = "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/utils/player.py"
    
    if os.path.exists(player_file):
        with open(player_file, 'r') as f:
            content = f.read()
            
        # Check for retry logic in run_test_suite_async
        if 'while retry_count <= max_retries and not success:' in content:
            print("✅ Found retry loop logic in Player.run_test_suite_async")
        else:
            print("❌ Retry loop logic NOT found in Player.run_test_suite_async")
            
        if 'retry_failed_tests' in content:
            print("✅ Found retry_failed_tests parameter handling in Player")
        else:
            print("❌ retry_failed_tests parameter NOT found in Player")
    else:
        print(f"❌ player.py not found at: {player_file}")

if __name__ == "__main__":
    print("🚀 Mobile App Automation Tool - Retry Functionality Debug Monitor")
    print("=" * 80)
    
    # Initial checks
    check_frontend_backend_flow()
    analyze_player_retry_logic()
    
    print("\n" + "=" * 80)
    print("📋 INSTRUCTIONS:")
    print("1. Keep this monitor running")
    print("2. In your browser, go to http://localhost:8080")
    print("3. Configure a test suite with retry count 2-3")
    print("4. Run a test suite with at least one failing test case")
    print("5. Watch this monitor for retry attempts and database updates")
    print("=" * 80)
    
    # Start monitoring
    monitor_execution_tracking()
