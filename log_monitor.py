#!/usr/bin/env python3
"""
Real-time log monitor for retry functionality debugging
"""
import subprocess
import time
import re

def monitor_application_logs():
    """Monitor application logs for retry-related messages"""
    print("🔍 Monitoring Application Logs for Retry Messages...")
    print("=" * 60)
    
    # Keywords to watch for
    retry_keywords = [
        'retry_failed_tests',
        'retryFailedTests', 
        'RETRYING TEST CASE',
        'retry_count',
        'max_retries',
        'Test case execution attempt',
        'will retry',
        'run_test_suite_async',
        'Test suite execution settings'
    ]
    
    try:
        # Use tail to follow the application logs
        process = subprocess.Popen(
            ['tail', '-f', '/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/appium_server.log'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        print("📋 Watching for these retry-related keywords:")
        for keyword in retry_keywords:
            print(f"  - {keyword}")
        print("=" * 60)
        
        while True:
            line = process.stdout.readline()
            if line:
                line = line.strip()
                # Check if line contains any retry keywords
                for keyword in retry_keywords:
                    if keyword.lower() in line.lower():
                        timestamp = time.strftime('%H:%M:%S')
                        print(f"🔍 [{timestamp}] RETRY LOG: {line}")
                        break
                        
    except KeyboardInterrupt:
        print("\n🛑 Log monitoring stopped")
        process.terminate()
    except Exception as e:
        print(f"❌ Error monitoring logs: {e}")

if __name__ == "__main__":
    monitor_application_logs()
